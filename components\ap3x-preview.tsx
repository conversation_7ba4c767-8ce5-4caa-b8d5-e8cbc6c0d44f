'use client'

import { AP3XInterpreter } from './ap3x-interpreter'
import { AP3XWeb } from './ap3x-web'
import { ExecutionResult } from '@/lib/types'

export function AP3XPreview({ result, refreshKey }: { result: ExecutionResult, refreshKey?: number }) {
  if (result.template === 'code-interpreter-v1') {
    return <AP3XInterpreter result={result} />
  }

  return <AP3XWeb result={result} refreshKey={refreshKey} />
}
