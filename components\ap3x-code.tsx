'use client'

import { But<PERSON> } from '@/components/ui/button'
import {
    <PERSON><PERSON><PERSON>,
    <PERSON>ltip<PERSON>ontent,
    TooltipProvider,
    TooltipTrigger,
} from '@/components/ui/tooltip'
import {
    Copy,
    Download,
    FileText
} from 'lucide-react'
import { useState } from 'react'
import { VSCodeEditor } from './vscode-editor'

// AP3X Copy button component
function AP3XCopyButton({ content, className }: { content: string; className?: string }) {
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(content)
    } catch (err) {
      console.error('Failed to copy text: ', err)
    }
  }

  return (
    <Button
      variant="ghost"
      size="icon"
      className={className}
      onClick={handleCopy}
    >
      <Copy className="h-4 w-4" />
    </Button>
  )
}

interface File {
  name: string
  content: string
  type?: string
}

interface AP3XCodeProps {
  files: File[]
}

export function AP3XCode({ files }: AP3XCodeProps) {
  return <VSCodeEditor files={files} />
}

// Alternative AP3XCode component with tabs and download functionality
export function AP3XCodeTabs({
  files,
}: {
  files: { name: string; content: string }[]
}) {
  const [currentFile, setCurrentFile] = useState(files[0]?.name || '')
  const currentFileContent = files.find(
    (file) => file.name === currentFile,
  )?.content

  function download(filename: string, content: string) {
    const blob = new Blob([content], { type: 'text/plain' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.style.display = 'none'
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    window.URL.revokeObjectURL(url)
    document.body.removeChild(a)
  }

  function downloadAll() {
    files.forEach((file) => {
      download(file.name, file.content)
    })
  }

  if (files.length === 0) {
    return (
      <div className="flex items-center justify-center h-full bg-background text-muted-foreground">
        <div className="text-center">
          <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <p className="text-sm">No files to display</p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col h-full bg-background">
      <div className="flex items-center justify-between p-3 border-b border-border bg-card">
        <div className="flex items-center space-x-1 overflow-x-auto">
          {files.map((file) => (
            <Button
              key={file.name}
              variant={currentFile === file.name ? 'default' : 'ghost'}
              size="sm"
              className="text-xs whitespace-nowrap"
              onClick={() => setCurrentFile(file.name)}
            >
              {file.name}
            </Button>
          ))}
        </div>
        <div className="flex items-center space-x-1">
          <TooltipProvider>
            <Tooltip delayDuration={0}>
              <TooltipTrigger asChild>
                <AP3XCopyButton
                  content={currentFileContent || ''}
                  className="h-8 w-8"
                />
              </TooltipTrigger>
              <TooltipContent side="bottom">Copy current file</TooltipContent>
            </Tooltip>
          </TooltipProvider>
          <TooltipProvider>
            <Tooltip delayDuration={0}>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => download(currentFile, currentFileContent || '')}
                >
                  <Download className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom">Download current file</TooltipContent>
            </Tooltip>
          </TooltipProvider>
          {files.length > 1 && (
            <TooltipProvider>
              <Tooltip delayDuration={0}>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-xs"
                    onClick={downloadAll}
                  >
                    Download All
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="bottom">Download all files</TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
        </div>
      </div>
      <div className="flex flex-col flex-1 overflow-hidden">
        <VSCodeEditor
          files={files}
        />
      </div>
    </div>
  )
}
