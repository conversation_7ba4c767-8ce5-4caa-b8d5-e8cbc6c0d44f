import { Message } from '@/lib/messages'
import { FragmentSchema } from '@/lib/schema'
import { ExecutionResult } from '@/lib/types'
import { DeepPartial } from 'ai'
import { LoaderIcon, Terminal } from 'lucide-react'
import { useEffect } from 'react'
import { EnhancedMessage } from './enhanced-message'
import { StreamingStatus, StreamingUpdate } from './streaming-status'

export function AP3XChat({
  messages,
  isLoading,
  setCurrentPreview,
  streamingUpdates = [],
}: {
  messages: Message[]
  isLoading: boolean
  setCurrentPreview: (preview: {
    fragment: DeepPartial<FragmentSchema> | undefined
    result: ExecutionResult | undefined
  }) => void
  streamingUpdates?: StreamingUpdate[]
}) {
  useEffect(() => {
    const chatContainer = document.getElementById('ap3x-chat-container')
    if (chatContainer) {
      chatContainer.scrollTop = chatContainer.scrollHeight
    }
  }, [JSON.stringify(messages), isLoading])

  return (
    <div
      id="ap3x-chat-container"
      className="flex flex-col pb-12 gap-3 overflow-y-auto max-h-full px-4 scrollbar-thin"
    >
      {messages.map((message: Message, index: number) => (
        <div key={index} className="flex flex-col gap-3">
          <EnhancedMessage
            message={message}
            isLatest={index === messages.length - 1}
          />

          {message.object && (
            <div
              onClick={() =>
                setCurrentPreview({
                  fragment: message.object,
                  result: message.result,
                })
              }
              className="py-3 pl-3 w-full md:w-max flex items-center border border-border rounded-xl select-none hover:bg-accent/50 hover:cursor-pointer transition-colors duration-200 bg-card"
            >
              <div className="rounded-lg w-10 h-10 bg-primary/10 self-stretch flex items-center justify-center">
                <Terminal strokeWidth={2} className="text-primary w-5 h-5" />
              </div>
              <div className="pl-3 pr-4 flex flex-col">
                <span className="font-semibold font-sans text-sm text-foreground">
                  {message.object.title}
                </span>
                <span className="font-sans text-xs text-muted-foreground">
                  Click to view in AP3X
                </span>
              </div>
            </div>
          )}
        </div>
      ))}
      
      {/* Streaming status */}
      {streamingUpdates.length > 0 && (
        <StreamingStatus isActive={isLoading} updates={streamingUpdates} />
      )}
      
      {isLoading && (
        <div className="flex items-center gap-2 text-sm text-muted-foreground px-4 py-2">
          <LoaderIcon strokeWidth={2} className="animate-spin w-4 h-4" />
          <span>AP3X is generating...</span>
        </div>
      )}
    </div>
  )
}
