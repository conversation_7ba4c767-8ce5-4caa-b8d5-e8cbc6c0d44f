"use client"

import { useTab } from "@/contexts/TabContext"
import { FragmentSchema } from "@/lib/schema"
import { ExecutionResult } from "@/lib/types"
import { DeepPartial } from "ai"
import { useEffect } from "react"

interface TabManagerProps {
  fragment?: DeepPartial<FragmentSchema>
  result?: ExecutionResult
  isGenerating?: boolean
}

export default function TabManager({ fragment, result, isGenerating }: TabManagerProps) {
  const { setPreviewEnabled, setCodeEnabled, setHasGeneratedCode, setActiveTab } = useTab()

  // Switch to code tab when generation starts
  useEffect(() => {
    if (isGenerating) {
      setActiveTab('code')
    }
  }, [isGenerating, setActiveTab])

  // Enable tabs when code is generated
  useEffect(() => {
    if (fragment && result) {
      setHasGeneratedCode(true)
      setCodeEnabled(true)
      setPreviewEnabled(true)
      // Don't automatically switch tabs - let user stay on current tab
    }
  }, [fragment, result, setHasGeneratedCode, setCodeEnabled, setPreviewEnabled])

  return null // This component doesn't render anything
}
