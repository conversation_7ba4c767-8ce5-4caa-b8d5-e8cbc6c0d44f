import {
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectLabel,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select'
import { LLMModel, LLMModelConfig } from '@/lib/models'
import { TemplateId, Templates } from '@/lib/templates'
import 'core-js/features/object/group-by.js'
import { Sparkles } from 'lucide-react'
import Image from 'next/image'

export function AP3XChatPicker({
  templates,
  selectedTemplate,
  onSelectedTemplateChange,
  models,
  languageModel,
  onLanguageModelChange,
}: {
  templates: Templates
  selectedTemplate: 'auto' | TemplateId
  onSelectedTemplateChange: (template: 'auto' | TemplateId) => void
  models: LLMModel[]
  languageModel: LLMModelConfig
  onLanguageModelChange: (config: LLMModelConfig) => void
}) {
  return (
    <div className="flex items-center space-x-2">
      <div className="flex flex-col">
        <Select
          name="ap3x-template"
          defaultValue={selectedTemplate}
          onValueChange={onSelectedTemplateChange}
        >
          <SelectTrigger className="whitespace-nowrap border-none shadow-none focus:ring-0 px-0 py-0 h-6 text-xs text-muted-foreground hover:text-foreground transition-colors">
            <SelectValue placeholder="Select AP3X persona" />
          </SelectTrigger>
          <SelectContent side="top" className="bg-background border-border">
            <SelectGroup>
              <SelectLabel className="text-foreground">AP3X Persona</SelectLabel>
              <SelectItem value="auto" className="hover:bg-accent">
                <div className="flex items-center space-x-2">
                  <Sparkles
                    className="flex text-primary"
                    width={14}
                    height={14}
                  />
                  <span className="text-foreground">Auto</span>
                </div>
              </SelectItem>
              {Object.entries(templates).map(([templateId, template]) => (
                <SelectItem key={templateId} value={templateId} className="hover:bg-accent">
                  <div className="flex items-center space-x-2">
                    <Image
                      src={`/thirdparty/templates/${templateId}.svg`}
                      alt={templateId}
                      width={14}
                      height={14}
                      className="rounded-sm"
                    />
                    <span className="text-foreground">{template.name}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectGroup>
          </SelectContent>
        </Select>
      </div>
      <div className="flex flex-col">
        <Select
          name="ap3x-model"
          value={languageModel.model}
          onValueChange={(model) => {
            const selectedModel = models.find((m) => m.id === model)
            if (selectedModel) {
              onLanguageModelChange({
                model: selectedModel.id,
                apiKey: languageModel.apiKey,
                baseURL: languageModel.baseURL,
              })
            }
          }}
        >
          <SelectTrigger className="whitespace-nowrap border-none shadow-none focus:ring-0 px-0 py-0 h-6 text-xs text-muted-foreground hover:text-foreground transition-colors">
            <SelectValue placeholder="Select AP3X model" />
          </SelectTrigger>
          <SelectContent side="top" className="bg-background border-border">
            <SelectGroup>
              <SelectLabel className="text-foreground">AP3X Models</SelectLabel>
              {Object.entries(
                Object.groupBy(models, (model) => model.provider)
              ).map(([provider, models]) => (
                <SelectGroup key={provider}>
                  <SelectLabel className="text-muted-foreground text-xs">
                    {provider}
                  </SelectLabel>
                  {models?.map((model) => (
                    <SelectItem key={model.id} value={model.id} className="hover:bg-accent">
                      <div className="flex items-center space-x-2">
                        <Image
                          src={`/thirdparty/logos/${model.providerId}.svg`}
                          alt={model.provider}
                          width={14}
                          height={14}
                          className="rounded-sm"
                        />
                        <span className="text-foreground">{model.name}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectGroup>
              ))}
            </SelectGroup>
          </SelectContent>
        </Select>
      </div>
    </div>
  )
}
