'use client'

import { StreamingUpdate } from '@/components/streaming-status'
import { Message } from '@/lib/messages'
import { useCallback, useRef, useState } from 'react'

export interface StreamingChatState {
  messages: Message[]
  isLoading: boolean
  streamingUpdates: StreamingUpdate[]
  currentFile: string | null
}

export function useStreamingChat() {
  const [state, setState] = useState<StreamingChatState>({
    messages: [],
    isLoading: false,
    streamingUpdates: [],
    currentFile: null
  })
  
  const streamingTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  const addMessage = useCallback((message: Message) => {
    setState(prev => ({
      ...prev,
      messages: [...prev.messages, message]
    }))
  }, [])

  const addStreamingUpdate = useCallback((update: Omit<StreamingUpdate, 'timestamp'>) => {
    const newUpdate: StreamingUpdate = {
      ...update,
      timestamp: Date.now()
    }
    
    setState(prev => ({
      ...prev,
      streamingUpdates: [...prev.streamingUpdates, newUpdate],
      currentFile: update.fileName || prev.currentFile
    }))
  }, [])

  const startStreaming = useCallback(() => {
    setState(prev => ({
      ...prev,
      isLoading: true,
      streamingUpdates: [],
      currentFile: null
    }))
  }, [])

  const stopStreaming = useCallback(() => {
    setState(prev => ({
      ...prev,
      isLoading: false,
      streamingUpdates: [],
      currentFile: null
    }))
    
    if (streamingTimeoutRef.current) {
      clearTimeout(streamingTimeoutRef.current)
    }
  }, [])

  // Simulate realistic file generation streaming
  const simulateFileGeneration = useCallback((files: string[]) => {
    startStreaming()
    
    const updates: Array<Omit<StreamingUpdate, 'timestamp'>> = [
      { type: 'analysis', message: 'Analyzing requirements and planning structure...' },
      { type: 'generation', message: 'Setting up project configuration...' },
    ]
    
    // Add file-specific updates
    files.forEach((fileName) => {
      updates.push(
        { type: 'file', message: `Creating ${fileName}...`, fileName },
        { type: 'file', message: `Writing code for ${fileName}...`, fileName },
        { type: 'file', message: `Optimizing ${fileName}...`, fileName }
      )
    })
    
    updates.push(
      { type: 'generation', message: 'Finalizing application structure...' },
      { type: 'completion', message: 'Application generated successfully!' }
    )

    // Send updates with realistic timing
    let currentIndex = 0
    const sendNextUpdate = () => {
      if (currentIndex < updates.length) {
        const update = updates[currentIndex]
        if (update) {
          addStreamingUpdate(update)
        }
        currentIndex++
        
        // Variable timing based on update type
        const delay = updates[currentIndex - 1]?.type === 'file' ? 1000 : 1500
        streamingTimeoutRef.current = setTimeout(sendNextUpdate, delay)
      } else {
        // Finish streaming after a short delay
        streamingTimeoutRef.current = setTimeout(stopStreaming, 1000)
      }
    }
    
    // Start the update sequence
    streamingTimeoutRef.current = setTimeout(sendNextUpdate, 500)
  }, [addStreamingUpdate, startStreaming, stopStreaming])

  const clearMessages = useCallback(() => {
    setState({
      messages: [],
      isLoading: false,
      streamingUpdates: [],
      currentFile: null
    })
  }, [])

  return {
    ...state,
    addMessage,
    addStreamingUpdate,
    startStreaming,
    stopStreaming,
    simulateFileGeneration,
    clearMessages
  }
}

// Enhanced hook that integrates with the existing useObject hook
export function useEnhancedChat(originalUseObjectResult: any) {
  const streamingChat = useStreamingChat()
  
  // Override the original submit to add streaming
  const enhancedSubmit = useCallback(async (data: any) => {
    // Extract potential file names from the request
    const potentialFiles = [
      'App.tsx',
      'globals.css', 
      'package.json',
      'components/Header.tsx',
      'components/Footer.tsx'
    ]
    
    // Start streaming simulation
    streamingChat.simulateFileGeneration(potentialFiles)
    
    // Call the original submit
    return originalUseObjectResult.submit(data)
  }, [originalUseObjectResult.submit, streamingChat])

  return {
    ...originalUseObjectResult,
    ...streamingChat,
    submit: enhancedSubmit,
    // Override isLoading to include streaming state
    isLoading: originalUseObjectResult.isLoading || streamingChat.isLoading
  }
}
