'use client'

import { But<PERSON> } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import {
    ChevronDown,
    ChevronRight,
    Copy,
    FileText,
    Folder,
    FolderOpen
} from 'lucide-react'
// Dynamic import for Prism to avoid SSR issues
import { useEffect, useState } from 'react'
import './vscode-theme.css'

interface File {
  name: string
  content: string
  type?: string
}

interface VSCodeEditorProps {
  files: File[]
}

// Get file icon based on extension
function getFileIcon(fileName: string) {
  const ext = fileName.split('.').pop()?.toLowerCase()
  switch (ext) {
    case 'tsx':
    case 'jsx':
      return <FileText className="w-4 h-4 text-blue-400" />
    case 'ts':
    case 'js':
      return <FileText className="w-4 h-4 text-yellow-400" />
    case 'css':
      return <FileText className="w-4 h-4 text-blue-300" />
    case 'json':
      return <FileText className="w-4 h-4 text-green-400" />
    case 'md':
      return <FileText className="w-4 h-4 text-gray-400" />
    default:
      return <FileText className="w-4 h-4 text-gray-300" />
  }
}

// Get language for syntax highlighting
function getLanguage(fileName: string): string {
  const ext = fileName.split('.').pop()?.toLowerCase()
  switch (ext) {
    case 'tsx': return 'tsx'
    case 'jsx': return 'jsx'
    case 'ts': return 'typescript'
    case 'js': return 'javascript'
    case 'css': return 'css'
    case 'json': return 'json'
    case 'sh': return 'bash'
    default: return 'javascript'
  }
}

// File Explorer Component
function FileExplorer({ 
  files, 
  selectedFile, 
  onFileSelect 
}: { 
  files: File[]
  selectedFile: string | null
  onFileSelect: (fileName: string) => void 
}) {
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set(['root']))

  // Organize files into folder structure
  const fileStructure = files.reduce((acc, file) => {
    const parts = file.name.split('/')
    let current = acc
    
    for (let i = 0; i < parts.length - 1; i++) {
      const folder = parts[i]
      if (folder && !current[folder]) {
        current[folder] = {}
      }
      if (folder) {
        current = current[folder]
      }
    }
    
    const fileName = parts[parts.length - 1]
    if (fileName) {
      current[fileName] = file
    }
    return acc
  }, {} as any)

  const toggleFolder = (folderPath: string) => {
    const newExpanded = new Set(expandedFolders)
    if (newExpanded.has(folderPath)) {
      newExpanded.delete(folderPath)
    } else {
      newExpanded.add(folderPath)
    }
    setExpandedFolders(newExpanded)
  }

  const renderFileTree = (structure: any, path = '', depth = 0) => {
    return Object.entries(structure).map(([name, item]) => {
      const fullPath = path ? `${path}/${name}` : name
      const isFile = item && typeof item === 'object' && 'content' in item
      const isExpanded = expandedFolders.has(fullPath)
      
      if (isFile) {
        const fileItem = item as File
        return (
          <div
            key={fullPath}
            className={cn(
              'flex items-center gap-2 px-2 py-1 text-sm cursor-pointer hover:bg-gray-700/50 transition-colors',
              selectedFile === fileItem.name && 'bg-gray-600/50 text-white'
            )}
            style={{ paddingLeft: `${depth * 16 + 8}px` }}
            onClick={() => onFileSelect(fileItem.name)}
          >
            {getFileIcon(name)}
            <span className="truncate">{name}</span>
          </div>
        )
      } else {
        return (
          <div key={fullPath}>
            <div
              className="flex items-center gap-1 px-2 py-1 text-sm cursor-pointer hover:bg-gray-700/50 transition-colors text-gray-300"
              style={{ paddingLeft: `${depth * 16 + 8}px` }}
              onClick={() => toggleFolder(fullPath)}
            >
              {isExpanded ? (
                <ChevronDown className="w-4 h-4" />
              ) : (
                <ChevronRight className="w-4 h-4" />
              )}
              {isExpanded ? (
                <FolderOpen className="w-4 h-4 text-blue-400" />
              ) : (
                <Folder className="w-4 h-4 text-blue-400" />
              )}
              <span>{name}</span>
            </div>
            {isExpanded && (
              <div>
                {renderFileTree(item, fullPath, depth + 1)}
              </div>
            )}
          </div>
        )
      }
    })
  }

  return (
    <div className="h-full bg-gray-900 text-gray-300 overflow-y-auto">
      <div className="px-3 py-2 text-xs font-medium text-gray-400 uppercase tracking-wide border-b border-gray-700">
        Explorer
      </div>
      <div className="py-2">
        {renderFileTree(fileStructure)}
      </div>
    </div>
  )
}

// Code Editor Component
function CodeEditor({ 
  file, 
  onCopy 
}: { 
  file: File | null
  onCopy: (content: string) => void 
}) {
  const [highlightedCode, setHighlightedCode] = useState('')

  useEffect(() => {
    if (file) {
      const loadPrismAndHighlight = async () => {
        try {
          // Dynamic import to avoid SSR issues
          const Prism = (await import('prismjs')).default

          // Load language components
          await import('prismjs/components/prism-javascript' as any)
          await import('prismjs/components/prism-jsx' as any)
          await import('prismjs/components/prism-typescript' as any)
          await import('prismjs/components/prism-tsx' as any)
          await import('prismjs/components/prism-css' as any)
          await import('prismjs/components/prism-json' as any)
          await import('prismjs/components/prism-bash' as any)

          const language = getLanguage(file.name)
          const grammar = Prism.languages[language] || Prism.languages.javascript || {}
          const highlighted = Prism.highlight(
            file.content,
            grammar as any,
            language
          )
          setHighlightedCode(highlighted)
        } catch (error) {
          console.warn('Failed to load Prism.js:', error)
          setHighlightedCode(file.content)
        }
      }

      loadPrismAndHighlight()
    }
  }, [file])

  if (!file) {
    return (
      <div className="flex items-center justify-center h-full text-gray-400">
        <div className="text-center">
          <FileText className="w-16 h-16 mx-auto mb-4 opacity-50" />
          <p className="text-lg">Select a file to view its contents</p>
        </div>
      </div>
    )
  }

  const lines = file.content.split('\n')

  return (
    <div className="h-full bg-gray-900 text-gray-300 overflow-hidden">
      {/* File Tab */}
      <div className="flex items-center bg-gray-800 border-b border-gray-700">
        <div className="flex items-center gap-2 px-4 py-2 bg-gray-900 border-r border-gray-700">
          {getFileIcon(file.name)}
          <span className="text-sm">{file.name}</span>
        </div>
        <div className="flex-1"></div>
        <Button
          variant="ghost"
          size="sm"
          className="h-8 w-8 p-0 text-gray-400 hover:text-white hover:bg-gray-700"
          onClick={() => onCopy(file.content)}
        >
          <Copy className="w-4 h-4" />
        </Button>
      </div>

      {/* Code Content */}
      <div className="flex h-full overflow-hidden">
        {/* Line Numbers */}
        <div className="bg-gray-900 text-gray-500 text-sm font-mono select-none border-r border-gray-700 px-2 py-4">
          {lines.map((_, index) => (
            <div key={index} className="text-right leading-6">
              {index + 1}
            </div>
          ))}
        </div>

        {/* Code */}
        <div className="flex-1 overflow-auto">
          <pre className="p-4 text-sm font-mono leading-6">
            {highlightedCode ? (
              <code
                className={`language-${getLanguage(file.name)}`}
                dangerouslySetInnerHTML={{ __html: highlightedCode }}
              />
            ) : (
              <code className="text-gray-300">
                {file.content}
              </code>
            )}
          </pre>
        </div>
      </div>
    </div>
  )
}

export function VSCodeEditor({ files }: VSCodeEditorProps) {
  const [selectedFile, setSelectedFile] = useState<string | null>(
    files.length > 0 ? (files[0]?.name || null) : null
  )

  const currentFile = files.find(f => f.name === selectedFile) || null

  const handleCopy = async (content: string) => {
    try {
      await navigator.clipboard.writeText(content)
    } catch (err) {
      console.error('Failed to copy text: ', err)
    }
  }

  if (files.length === 0) {
    return (
      <div className="flex items-center justify-center h-full bg-gray-900 text-gray-400">
        <div className="text-center">
          <FileText className="w-16 h-16 mx-auto mb-4 opacity-50" />
          <p className="text-lg">No files available</p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex h-full bg-gray-900 text-gray-300">
      {/* File Explorer */}
      <div className="w-64 border-r border-gray-700">
        <FileExplorer
          files={files}
          selectedFile={selectedFile}
          onFileSelect={setSelectedFile}
        />
      </div>

      {/* Code Editor */}
      <div className="flex-1">
        <CodeEditor
          file={currentFile}
          onCopy={handleCopy}
        />
      </div>
    </div>
  )
}
