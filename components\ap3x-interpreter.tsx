import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { ExecutionResultInterpreter } from '@/lib/types'
import { Terminal } from 'lucide-react'
import Image from 'next/image'

function AP3XLogsOutput({
  stdout,
  stderr,
}: {
  stdout: string[]
  stderr: string[]
}) {
  if (stdout.length === 0 && stderr.length === 0) return null

  return (
    <div className="w-full h-32 max-h-32 overflow-y-auto flex flex-col items-start justify-start space-y-1 p-4 bg-background border-t border-border">
      {stdout &&
        stdout.length > 0 &&
        stdout.map((out: string, index: number) => (
          <pre key={index} className="text-xs text-foreground font-mono">
            {out}
          </pre>
        ))}
      {stderr &&
        stderr.length > 0 &&
        stderr.map((err: string, index: number) => (
          <pre key={index} className="text-xs text-destructive font-mono">
            {err}
          </pre>
        ))}
    </div>
  )
}

export function AP3XInterpreter({
  result,
}: {
  result: ExecutionResultInterpreter
}) {
  const { cellResults, stdout, stderr, runtimeError } = result

  // The AI-generated code experienced runtime error
  if (runtimeError) {
    const { name, value, traceback } = runtimeError
    return (
      <div className="p-4">
        <Alert variant="destructive" className="border-destructive/50 bg-destructive/10">
          <Terminal className="h-4 w-4" />
          <AlertTitle className="text-destructive">
            {name}: {value}
          </AlertTitle>
          <AlertDescription className="font-mono whitespace-pre-wrap text-destructive/90">
            {traceback}
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  // Cell results can contain text, pdfs, images, and code (html, latex, json)
  // TODO: Show all results
  // TODO: Check other formats than `png`
  if (cellResults.length > 0 && cellResults[0]) {
    const imgInBase64 = cellResults[0].png
    return (
      <div className="flex flex-col h-full bg-background">
        <div className="w-full flex-1 p-4 flex items-start justify-center border-b border-border">
          <Image
            src={`data:image/png;base64,${imgInBase64}`}
            alt="AP3X Execution Result"
            width={600}
            height={400}
            className="max-w-full h-auto rounded-lg shadow-lg"
          />
        </div>
        <AP3XLogsOutput stdout={stdout} stderr={stderr} />
      </div>
    )
  }

  // No cell results, but there is stdout or stderr
  if (stdout.length > 0 || stderr.length > 0) {
    return (
      <div className="h-full bg-background">
        <AP3XLogsOutput stdout={stdout} stderr={stderr} />
      </div>
    )
  }

  return (
    <div className="flex items-center justify-center h-full bg-background text-muted-foreground">
      <span className="text-sm">No output or logs</span>
    </div>
  )
}
