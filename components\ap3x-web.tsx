import { Button } from '@/components/ui/button'
import {
  <PERSON><PERSON><PERSON>,
  Too<PERSON><PERSON><PERSON>ontent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import { ExecutionResultWeb } from '@/lib/types'
import { Copy, RotateCw } from 'lucide-react'
import { useState } from 'react'

// AP3X Copy button component
function AP3XCopyButton({ content, className, variant = "link" }: {
  content: string;
  className?: string;
  variant?: "ghost" | "link" | "default" | "destructive" | "outline" | "secondary";
}) {
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(content)
    } catch (err) {
      console.error('Failed to copy text: ', err)
    }
  }

  return (
    <Button
      variant={variant}
      size="icon"
      className={className}
      onClick={handleCopy}
    >
      <Copy className="h-4 w-4" />
    </Button>
  )
}

export function AP3XWeb({ result, refreshKey }: { result: ExecutionResultWeb, refreshKey?: number }) {
  const [iframeKey, setIframeKey] = useState(refreshKey || 0)

  if (!result) return null

  function refreshIframe() {
    setIframeKey((prevKey) => prevKey + 1)
  }

  return (
    <div className="flex flex-col w-full h-full">
      <iframe
        key={iframeKey}
        className="h-full w-full"
        sandbox="allow-forms allow-scripts allow-same-origin"
        loading="lazy"
        src={result.url}
      />
      <div className="p-2 border-t border-border">
        <div className="flex items-center bg-muted dark:bg-white/10 rounded-2xl">
          <TooltipProvider>
            <Tooltip delayDuration={0}>
              <TooltipTrigger asChild>
                <Button
                  variant="link"
                  className="text-muted-foreground hover:text-foreground transition-colors"
                  onClick={refreshIframe}
                >
                  <RotateCw className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Refresh</TooltipContent>
            </Tooltip>
          </TooltipProvider>
          <span className="text-muted-foreground text-xs flex-1 text-ellipsis overflow-hidden whitespace-nowrap px-2">
            {result.url}
          </span>
          <TooltipProvider>
            <Tooltip delayDuration={0}>
              <TooltipTrigger asChild>
                <AP3XCopyButton
                  variant="link"
                  content={result.url}
                  className="text-muted-foreground hover:text-foreground transition-colors"
                />
              </TooltipTrigger>
              <TooltipContent>Copy URL</TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>
    </div>
  )
}
