"use client"

import <PERSON>b<PERSON><PERSON> from "@/components/OrbNav"
import { AP3XCode } from "@/components/ap3x-code"
import { AP3XPreview } from "@/components/ap3x-preview"
import { useTab } from "@/contexts/TabContext"
import { FragmentSchema } from "@/lib/schema"
import { ExecutionResult } from "@/lib/types"
import { DeepPartial } from "ai"
import dynamic from "next/dynamic"
import { Suspense } from "react"

// Dynamically import components to avoid SSR issues
const Globe = dynamic(() => import("@/components/Globe"), { ssr: false })

interface TabContentProps {
  sandboxId?: string
  fragment?: DeepPartial<FragmentSchema>
  result?: ExecutionResult
  refreshKey?: number
}

export default function TabContent({ sandboxId: _sandboxId, fragment, result, refreshKey }: TabContentProps) {
  const { activeTab } = useTab()

  const renderContent = () => {
    switch (activeTab) {
      case 'orb':
        return (
          <div className="relative h-full">
            <Suspense
              fallback={
                <div className="flex items-center justify-center h-full">
                  <div className="text-gray-400">Loading The Orb...</div>
                </div>
              }
            >
              <Globe />
              <OrbNav />
            </Suspense>
          </div>
        )

      case 'preview':
        if (result) {
          return (
            <div className="h-full w-full">
              <AP3XPreview result={result} refreshKey={refreshKey} />
            </div>
          )
        }
        return (
          <div className="flex items-center justify-center h-full">
            <div className="text-gray-400">No preview available</div>
          </div>
        )

      case 'code':
        // Multi-file application support
        if (fragment?.is_multi_file && fragment?.files && fragment.files.length > 0) {
          return (
            <div className="h-full w-full">
              <AP3XCode
                files={fragment.files.map(file => file ? ({
                  name: file.file_path || file.file_name || 'unknown',
                  content: file.file_content || '',
                }) : { name: 'unknown', content: '' }).filter(Boolean)}
              />
            </div>
          )
        }
        // Single-file application (legacy support)
        else if (fragment?.code && fragment?.file_path) {
          return (
            <div className="h-full w-full">
              <AP3XCode
                files={[
                  {
                    name: fragment.file_path,
                    content: fragment.code,
                  },
                ]}
              />
            </div>
          )
        }
        return (
          <div className="flex items-center justify-center h-full">
            <div className="text-gray-400">No code available</div>
          </div>
        )

      default:
        return (
          <div className="flex items-center justify-center h-full">
            <div className="text-gray-400">Unknown tab</div>
          </div>
        )
    }
  }

  return (
    <div className="h-full bg-black overflow-auto">
      {renderContent()}
    </div>
  )
}
