"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>rollArea } from "@/components/ui/scroll-area"
import { MessageSquare, Plus, Trash2 } from "lucide-react"
import { useState } from "react"

interface ChatHistorySidebarProps {
  onSelectChat?: (chatId: string) => void
  currentChatId?: string
}

export default function ChatHistorySidebar({ onSelectChat: _onSelectChat, currentChatId }: ChatHistorySidebarProps) {
  const [isVisible, setIsVisible] = useState(false)
  // Mock chat data for UI display
  const mockChats = [
    { id: '1', title: 'New Chat', timestamp: new Date(), hasCode: false },
    { id: '2', title: 'React Component', timestamp: new Date(Date.now() - 86400000), hasCode: true },
    { id: '3', title: 'API Integration', timestamp: new Date(Date.now() - 172800000), hasCode: true },
  ]

  return (
    <>
      {/* Hover trigger area */}
      <div
        className="absolute top-0 left-0 bottom-0 w-4 z-50"
        onMouseEnter={() => setIsVisible(true)}
      />

      {/* Sidebar */}
      <aside
        className={`absolute top-0 left-0 bottom-0 w-[270px] bg-[#0a0a0a] border-r border-gray-800 flex flex-col transition-transform duration-300 z-40 ${
          isVisible ? 'translate-x-0' : '-translate-x-full'
        }`}
        onMouseLeave={() => setIsVisible(false)}
      >
        {/* Header */}
        <div className="p-4 border-b border-gray-800">
          <Button
            className="w-full bg-white text-black hover:bg-gray-200 flex items-center justify-center space-x-2"
            disabled
          >
            <Plus className="h-4 w-4" />
            <span>New Chat</span>
          </Button>
        </div>

        {/* Chat History */}
        <ScrollArea className="flex-1">
          <div className="p-2 space-y-1">
            {mockChats.map((chat) => (
              <div
                key={chat.id}
                className={`group relative p-3 rounded-lg cursor-pointer transition-colors ${
                  currentChatId === chat.id
                    ? 'bg-gray-800 text-white'
                    : 'text-gray-400 hover:bg-gray-900 hover:text-gray-300'
                }`}
              >
                <div className="flex items-start space-x-3">
                  <MessageSquare className="h-4 w-4 mt-0.5 flex-shrink-0" />
                  <div className="flex-1 min-w-0">
                    <div className="text-sm font-medium truncate">
                      {chat.title}
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      {chat.timestamp.toLocaleDateString()}
                    </div>
                    {chat.hasCode && (
                      <div className="text-xs text-blue-400 mt-1">
                        Contains code
                      </div>
                    )}
                  </div>
                </div>

                {/* Delete button */}
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute top-2 right-2 h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity text-gray-400 hover:text-red-400"
                  disabled
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
            ))}
          </div>
        </ScrollArea>

        {/* Footer */}
        <div className="p-4 border-t border-gray-800">
          <div className="text-xs text-gray-500 text-center">
            Chat history disabled - UI only
          </div>
        </div>
      </aside>
    </>
  )
}
