'use client'

import {
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>ontent,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    Too<PERSON><PERSON><PERSON>rigger,
} from '@/components/ui/tooltip'
import { cn } from '@/lib/utils'
import { Download, FileText, X } from 'lucide-react'
import Prism from 'prismjs'
import 'prismjs/components/prism-bash'
import 'prismjs/components/prism-css'
import 'prismjs/components/prism-javascript'
import 'prismjs/components/prism-json'
import 'prismjs/components/prism-jsx'
import 'prismjs/components/prism-python'
import 'prismjs/components/prism-sql'
import 'prismjs/components/prism-tsx'
import 'prismjs/components/prism-typescript'
import { useEffect, useState } from 'react'
import { Button } from './ui/button'
import { CopyButton } from './ui/copy-button'

interface FileTab {
  name: string
  content: string
  isModified?: boolean
}

interface CodeEditorProps {
  files: Array<{ name: string; content: string }>
  selectedFile: string | null
  onFileSelect: (fileName: string) => void
}

// Get language for syntax highlighting
const getLanguage = (fileName: string): string => {
  const ext = fileName.split('.').pop()?.toLowerCase()
  
  switch (ext) {
    case 'tsx':
      return 'tsx'
    case 'jsx':
      return 'jsx'
    case 'ts':
      return 'typescript'
    case 'js':
      return 'javascript'
    case 'css':
    case 'scss':
      return 'css'
    case 'json':
      return 'json'
    case 'sql':
      return 'sql'
    case 'sh':
    case 'bash':
      return 'bash'
    case 'py':
      return 'python'
    default:
      return 'javascript'
  }
}

// Download file function
const downloadFile = (fileName: string, content: string) => {
  const blob = new Blob([content], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = fileName.split('/').pop() || fileName
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

// Enhanced code view with line numbers
const CodeView = ({ code, language, fileName: _fileName }: { code: string; language: string; fileName: string }) => {
  useEffect(() => {
    Prism.highlightAll()
  }, [code, language])

  const lines = code.split('\n')

  return (
    <div className="flex h-full bg-gray-900 text-gray-100 font-mono text-sm">
      {/* Line numbers */}
      <div className="flex-shrink-0 bg-gray-800 border-r border-gray-700 px-3 py-4 text-gray-400 text-right select-none">
        {lines.map((_, index) => (
          <div key={index} className="leading-6">
            {index + 1}
          </div>
        ))}
      </div>
      
      {/* Code content */}
      <div className="flex-1 overflow-auto code-editor-scrollbar">
        <pre className="p-4 m-0 bg-transparent">
          <code className={`language-${language}`}>{code}</code>
        </pre>
      </div>
    </div>
  )
}

// File tab component
const FileTab = ({ 
  file, 
  isActive, 
  onSelect, 
  onClose 
}: { 
  file: FileTab
  isActive: boolean
  onSelect: () => void
  onClose: () => void
}) => {
  return (
    <div
      className={cn(
        'flex items-center gap-2 px-3 py-2 border-r border-border cursor-pointer group max-w-48',
        isActive
          ? 'bg-background border-b-2 border-b-primary'
          : 'bg-muted hover:bg-accent'
      )}
      onClick={onSelect}
    >
      <FileText className="h-4 w-4 text-muted-foreground flex-shrink-0" />
      <span className="text-sm truncate">
        {file.name.split('/').pop()}
      </span>
      {file.isModified && (
        <div className="w-2 h-2 bg-orange-400 rounded-full flex-shrink-0" />
      )}
      <button
        onClick={(e) => {
          e.stopPropagation()
          onClose()
        }}
        className="opacity-0 group-hover:opacity-100 hover:bg-accent rounded p-1 transition-opacity"
      >
        <X className="h-3 w-3" />
      </button>
    </div>
  )
}

export function CodeEditor({ files, selectedFile, onFileSelect }: CodeEditorProps) {
  const [openTabs, setOpenTabs] = useState<FileTab[]>([])
  const [activeTab, setActiveTab] = useState<string | null>(null)

  // Remove duplicate tabs on mount and when files change
  useEffect(() => {
    setOpenTabs(prev => {
      const uniqueTabs = prev.filter((tab, index, self) =>
        index === self.findIndex(t => t.name === tab.name)
      )
      return uniqueTabs
    })
  }, [files])

  // Update open tabs when selectedFile changes
  useEffect(() => {
    if (selectedFile) {
      const file = files.find(f => f.name === selectedFile)
      if (file) {
        setOpenTabs(prev => {
          const existingTab = prev.find(tab => tab.name === file.name)
          if (!existingTab) {
            const newTab: FileTab = {
              name: file.name,
              content: file.content
            }
            return [...prev, newTab]
          }
          return prev
        })
        setActiveTab(file.name)
      }
    }
  }, [selectedFile, files])

  // Close tab
  const closeTab = (fileName: string) => {
    setOpenTabs(prev => prev.filter(tab => tab.name !== fileName))
    if (activeTab === fileName) {
      const remainingTabs = openTabs.filter(tab => tab.name !== fileName)
      if (remainingTabs.length > 0) {
        const lastTab = remainingTabs[remainingTabs.length - 1]
        if (lastTab) {
          setActiveTab(lastTab.name)
          onFileSelect(lastTab.name)
        }
      } else {
        setActiveTab(null)
      }
    }
  }

  // Select tab
  const selectTab = (fileName: string) => {
    setActiveTab(fileName)
    onFileSelect(fileName)
  }

  const activeFile = files.find(f => f.name === activeTab)

  return (
    <div className="flex flex-col h-full bg-background">
      {/* Tab bar */}
      {openTabs.length > 0 && (
        <div className="flex border-b border-border bg-card overflow-x-auto">
          {openTabs.map((tab, index) => (
            <FileTab
              key={`tab-${tab.name}-${index}`}
              file={tab}
              isActive={activeTab === tab.name}
              onSelect={() => selectTab(tab.name)}
              onClose={() => closeTab(tab.name)}
            />
          ))}
        </div>
      )}

      {/* Editor header */}
      {activeFile && (
        <div className="flex items-center justify-between px-4 py-2 border-b border-border bg-card">
          <div className="flex items-center gap-2">
            <FileText className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium text-foreground">
              {activeFile.name}
            </span>
          </div>
          
          <div className="flex items-center gap-2">
            <TooltipProvider>
              <Tooltip delayDuration={0}>
                <TooltipTrigger asChild>
                  <CopyButton
                    content={activeFile.content}
                    className="text-muted-foreground"
                  />
                </TooltipTrigger>
                <TooltipContent side="bottom">Copy code</TooltipContent>
              </Tooltip>
            </TooltipProvider>
            
            <TooltipProvider>
              <Tooltip delayDuration={0}>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="text-muted-foreground"
                    onClick={() => downloadFile(activeFile.name, activeFile.content)}
                  >
                    <Download className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="bottom">Download file</TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
      )}

      {/* Editor content */}
      <div className="flex-1 overflow-hidden">
        {activeFile ? (
          <CodeView
            code={activeFile.content}
            language={getLanguage(activeFile.name)}
            fileName={activeFile.name}
          />
        ) : (
          <div className="flex items-center justify-center h-full text-muted-foreground">
            <div className="text-center">
              <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium mb-2">No file selected</p>
              <p className="text-sm">Select a file from the explorer to view its contents</p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
