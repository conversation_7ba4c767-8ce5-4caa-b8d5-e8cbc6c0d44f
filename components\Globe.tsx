"use client"

import { useEffect, useRef, useState } from "react"
import * as THREE from "three"
import { Color } from "three"
import { OrbitControls } from "three/addons/controls/OrbitControls.js"

export default function Globe() {
  const mountRef = useRef<HTMLDivElement>(null)
  const [, setIsHighResLoaded] = useState(false)
  const [showHint, setShowHint] = useState(true)

  useEffect(() => {
    if (!mountRef.current) return
    const currentMount = mountRef.current

    // Create scene, camera, and renderer
    const scene = new THREE.Scene()
    const camera = new THREE.PerspectiveCamera(75, currentMount.clientWidth / currentMount.clientHeight, 0.1, 1000)
    const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true })
    renderer.setSize(currentMount.clientWidth, currentMount.clientHeight)
    renderer.setPixelRatio(window.devicePixelRatio)
    currentMount.appendChild(renderer.domElement)

    // Create a starfield positioned far from the orb
    const starsGeometry = new THREE.BufferGeometry()
    const starsCount = 10000
    const positions = new Float32Array(starsCount * 3)
    const minDistance = 15 // Minimum distance from orb center
    const maxDistance = 1000 // Maximum distance for stars

    for (let i = 0; i < starsCount; i++) {
      let x, y, z, distance

      // Generate random positions until we get one far enough from the orb
      do {
        x = (Math.random() - 0.5) * 2 * maxDistance
        y = (Math.random() - 0.5) * 2 * maxDistance
        z = (Math.random() - 0.5) * 2 * maxDistance
        distance = Math.sqrt(x * x + y * y + z * z)
      } while (distance < minDistance)

      positions[i * 3] = x
      positions[i * 3 + 1] = y
      positions[i * 3 + 2] = z
    }
    starsGeometry.setAttribute("position", new THREE.BufferAttribute(positions, 3))
    const starsMaterial = new THREE.PointsMaterial({
      color: 0xffffff,
      size: 0.7,
      sizeAttenuation: true,
    })
    const stars = new THREE.Points(starsGeometry, starsMaterial)
    scene.add(stars)

    // Create an atmospheric glow using a custom shader
    const atmosphereVertexShader = `
      varying vec3 vNormal;
      void main() {
        vNormal = normalize(normalMatrix * normal);
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `
    const atmosphereFragmentShader = `
     uniform vec3 glowColor;
     varying vec3 vNormal;
     void main() {
       float intensity = pow(0.6 - dot(vNormal, vec3(0.0, 0.0, 1.0)), 2.0);
       gl_FragColor = vec4(glowColor, 1.0) * intensity;
     }
   `
    const atmosphereGeometry = new THREE.SphereGeometry(5.2, 32, 32)
    const atmosphereMaterial = new THREE.ShaderMaterial({
      vertexShader: atmosphereVertexShader,
      fragmentShader: atmosphereFragmentShader,
      blending: THREE.AdditiveBlending,
      side: THREE.BackSide,
      transparent: true,
      uniforms: {
        glowColor: { value: new Color(0x3a86ff) },
      },
    })
    const atmosphereMesh = new THREE.Mesh(atmosphereGeometry, atmosphereMaterial)
    scene.add(atmosphereMesh)

    // Wireframe globe removed for cleaner appearance

    // Solid globe removed - using only atmospheric glow effect

    // Add ambient light
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.5)
    scene.add(ambientLight)

    // Add point light
    const pointLight = new THREE.PointLight(0xffffff, 1)
    pointLight.position.set(10, 10, 10)
    scene.add(pointLight)

    camera.position.z = 10

    const controls = new OrbitControls(camera, renderer.domElement)
    controls.enableDamping = true
    controls.dampingFactor = 0.05
    controls.rotateSpeed = 0.5
    controls.enableZoom = false

    const colors = [
      new Color(0x3a86ff), // Blue
      new Color(0x8338ec), // Purple
      new Color(0xff006e), // Pink
      new Color(0xfb5607), // Orange
      new Color(0xffbe0b), // Yellow
    ]
    let colorIndex = 0
    let nextColorIndex = 1
    let colorT = 0
    const colorTransitionSpeed = 0.005

    const lerpColor = (a: Color, b: Color, t: number) => {
      const color = new Color()
      color.r = a.r + (b.r - a.r) * t
      color.g = a.g + (b.g - a.g) * t
      color.b = a.b + (b.b - a.b) * t
      return color
    }

    let animationId: number

    const animate = () => {
      animationId = requestAnimationFrame(animate)

      // Color transition logic
      colorT += colorTransitionSpeed
      if (colorT >= 1) {
        colorT = 0
        colorIndex = nextColorIndex
        nextColorIndex = (nextColorIndex + 1) % colors.length
      }

      const currentColor = lerpColor(
        colors[colorIndex] || colors[0] || new Color(0x00ff88),
        colors[nextColorIndex] || colors[0] || new Color(0x00ff88),
        colorT
      )

      // Update atmospheric glow color
      if (atmosphereMesh.material instanceof THREE.ShaderMaterial &&
          atmosphereMesh.material.uniforms.glowColor) {
        atmosphereMesh.material.uniforms.glowColor.value = currentColor
      }

      // Rotate the atmosphere and starfield for dynamic effect
      atmosphereMesh.rotation.y += 0.0005
      stars.rotation.y += 0.0001
      controls.update()
      renderer.render(scene, camera)
    }
    animate()

    // No transition needed - atmospheric glow is immediately visible
    setIsHighResLoaded(true)

    const handleResize = () => {
      if (currentMount) {
        camera.aspect = currentMount.clientWidth / currentMount.clientHeight
        camera.updateProjectionMatrix()
        renderer.setSize(currentMount.clientWidth, currentMount.clientHeight)
      }
    }
    window.addEventListener("resize", handleResize)

    const hintTimer = setTimeout(() => {
      setShowHint(false)
    }, 3000) // Hide hint after 3 seconds

    return () => {
      window.removeEventListener("resize", handleResize)
      cancelAnimationFrame(animationId)
      if (currentMount) {
        currentMount.removeChild(renderer.domElement)
      }
      controls.dispose()
      clearTimeout(hintTimer)
    }
  }, [])

  return (
    <div ref={mountRef} className="absolute inset-0 w-full h-full">
      {showHint && (
        <div className="absolute bottom-4 right-4 bg-black bg-opacity-30 text-white text-sm px-3 py-1 rounded-full transition-opacity duration-1000 opacity-80 hover:opacity-100 z-10">
          Drag to explore
        </div>
      )}
    </div>
  )
}
