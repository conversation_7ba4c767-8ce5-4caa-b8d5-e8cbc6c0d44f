"use client"

import Chat<PERSON><PERSON>ory<PERSON>idebar from "@/components/ChatHistorySidebar"
import Header from "@/components/Header"
import PreviewControls from "@/components/PreviewControls"
import TabContent from "@/components/TabContent"
import TabManager from "@/components/TabManager"
import { TabProvider } from "@/contexts/TabContext"
import { useState } from "react"

// AP3X imports
import { AP3XAuthDialog } from '@/components/ap3x-auth-dialog'
import { AP3XChat } from '@/components/ap3x-chat'
import { AP3XChatInput } from '@/components/ap3x-chat-input'
import { AP3XChatPicker } from '@/components/ap3x-chat-picker'
import { ViewType } from '@/components/auth'
import { ChatSettings } from '@/components/chat-settings'
import { useStreamingChat } from '@/hooks/useStreamingChat'
import { useAuth } from '@/lib/auth'
import { Message, toAISDKMessages, toMessageImage } from '@/lib/messages'
import { LLMModelConfig } from '@/lib/models'
import modelsList from '@/lib/models.json'
import { FragmentSchema, fragmentSchema as schema } from '@/lib/schema'
import { supabase } from '@/lib/supabase'
import templates, { TemplateId } from '@/lib/templates'
import { ExecutionResult } from '@/lib/types'
import { DeepPartial } from 'ai'
import { experimental_useObject as useObject } from 'ai/react'
import { usePostHog } from 'posthog-js/react'
import { useCallback } from 'react'
import { useLocalStorage } from 'usehooks-ts'

export default function Home() {
  // Fragments-main state management
  const [chatInput, setChatInput] = useLocalStorage('chat', '')
  const [files, setFiles] = useState<File[]>([])
  const [selectedTemplate, setSelectedTemplate] = useState<'auto' | TemplateId>('nextjs-developer')
  const [languageModel, setLanguageModel] = useLocalStorage<LLMModelConfig>(
    'languageModel',
    { model: 'anthropic/claude-3.5-sonnet' }
  )
  const [messages, setMessages] = useState<Message[]>([])
  const [_result, setResult] = useState<ExecutionResult | undefined>()
  const [_currentTab, setCurrentTab] = useState<'code' | 'fragment'>('code')
  const [_isPreviewLoading, setIsPreviewLoading] = useState(false)
  const [authDialog, setAuthDialog] = useState(false)
  const [authView, setAuthView] = useState<ViewType>('sign_in')
  const [isRateLimited, setIsRateLimited] = useState(false)
  const [errorMessage, setErrorMessage] = useState<string>('')
  const [currentPreview, setCurrentPreview] = useState<{
    fragment: DeepPartial<FragmentSchema> | undefined
    result: ExecutionResult | undefined
  } | null>(null)
  const [isGenerating, setIsGenerating] = useState(false)

  const [refreshKey, setRefreshKey] = useState(0)

  const handleRefresh = () => {
    setRefreshKey(prev => prev + 1)
  }

  const { session, userTeam, logout: _logout } = useAuth(setAuthDialog, setAuthView)
  const posthog = usePostHog()
  const streamingChat = useStreamingChat()

  const filteredModels = modelsList.models.filter((model) => {
    if (process.env.NEXT_PUBLIC_HIDE_LOCAL_MODELS) {
      return model.providerId !== 'ollama'
    }
    return true
  })

  const currentModel = filteredModels.find(
    (model) => model.id === languageModel.model,
  )
  const currentTemplate =
    selectedTemplate === 'auto'
      ? templates
      : { [selectedTemplate]: templates[selectedTemplate] }

  const { object: _object, submit, isLoading, stop, error } = useObject({
    api: '/api/chat',
    schema,
    onError: (error) => {
      console.error('Error submitting request:', error)
      setIsGenerating(false)
      if (error.message.includes('limit')) {
        setIsRateLimited(true)
      }
      setErrorMessage(error.message)
    },
    onFinish: async ({ object: fragment, error }) => {
      // Stop streaming when generation is complete
      streamingChat.stopStreaming()
      setIsGenerating(false)

      if (!error && fragment) {
        console.log('fragment', fragment)
        setIsPreviewLoading(true)
        posthog.capture('fragment_generated', {
          template: fragment?.template,
        })

        try {
          const response = await fetch('/api/sandbox', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              fragment,
              userID: session?.user?.id,
              teamID: userTeam?.id,
              accessToken: session?.access_token,
            }),
          })

          if (!response.ok) {
            const errorData = await response.json().catch(() => ({
              error: 'Failed to parse error response',
              status: response.status
            }))

            console.error('Sandbox API error:', errorData)
            setErrorMessage(`Sandbox creation failed: ${errorData.error || 'Unknown error'}`)
            setIsPreviewLoading(false)
            return
          }

          const result = await response.json()
          console.log('result', result)
          posthog.capture('sandbox_created', { url: result.url })

          setResult(result)
          setCurrentPreview({ fragment, result })
          // Keep the current tab as 'code' to show the generated code
          setIsPreviewLoading(false)
        } catch (error) {
          console.error('Error creating sandbox:', error)
          setErrorMessage(`Failed to create sandbox: ${error instanceof Error ? error.message : 'Unknown error'}`)
          setIsPreviewLoading(false)
        }
      }
    },
  })

  const addMessage = useCallback((message: Message) => {
    setMessages((prev) => [...prev, message])
    return [...messages, message]
  }, [messages])

  const handleSubmitAuth = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()

    if (!session) {
      return setAuthDialog(true)
    }

    if (isLoading) {
      stop()
    }

    const content: Message['content'] = [{ type: 'text', text: chatInput }]
    const images = await toMessageImage(files)

    if (images.length > 0) {
      images.forEach((image) => {
        content.push({ type: 'image', image })
      })
    }

    const updatedMessages = addMessage({
      role: 'user',
      content,
    })

    // Start streaming simulation
    const potentialFiles = ['App.tsx', 'globals.css', 'package.json', 'components/Header.tsx']
    streamingChat.simulateFileGeneration(potentialFiles)
    setIsGenerating(true)

    submit({
      userID: session?.user?.id,
      teamID: userTeam?.id,
      messages: toAISDKMessages(updatedMessages),
      template: currentTemplate,
      model: currentModel,
      config: languageModel,
    })

    setChatInput('')
    setFiles([])
    // Tab switching will be handled by TabManager when code is generated

    posthog.capture('chat_submit', {
      template: selectedTemplate,
      model: languageModel.model,
    })
  }

  return (
    <TabProvider>
      <TabManager
        fragment={currentPreview?.fragment}
        result={currentPreview?.result}
        isGenerating={isGenerating}
      />
      <div className="h-screen w-full bg-black text-white flex flex-col font-sans">
        <Header />
        <div className="flex-1 relative">
          <ChatHistorySidebar />

          {/* Enhanced ChatSidebar with fragments functionality */}
          <aside className="absolute top-0 left-0 bottom-0 w-[540px] p-3 pt-2">
            <div className="h-full bg-[#111111] rounded-xl flex flex-col overflow-hidden">
              <div className="px-3 py-1.5 border-b border-gray-800">
                <div className="flex items-center justify-between">
                  <div className="text-white">
                    <h2 className="text-lg font-semibold">Chat</h2>
                    <div className="text-xs text-gray-400 mt-1">
                      AI Coding Assistant
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex-1 overflow-hidden">
                <AP3XChat
                  messages={messages}
                  isLoading={isLoading || streamingChat.isLoading}
                  setCurrentPreview={setCurrentPreview}
                  streamingUpdates={streamingChat.streamingUpdates}
                />
              </div>

              <div className="border-t border-gray-800 p-2">
                <AP3XChatInput
                  retry={() => {}}
                  isErrored={error !== undefined}
                  errorMessage={errorMessage}
                  isLoading={isLoading}
                  isRateLimited={isRateLimited}
                  stop={stop}
                  input={chatInput}
                  handleInputChange={(e) => setChatInput(e.target.value)}
                  handleSubmit={handleSubmitAuth}
                  isMultiModal={currentModel?.multiModal || false}
                  files={files}
                  handleFileChange={setFiles}
                >
                  <AP3XChatPicker
                    templates={templates}
                    selectedTemplate={selectedTemplate}
                    onSelectedTemplateChange={setSelectedTemplate}
                    models={filteredModels}
                    languageModel={languageModel}
                    onLanguageModelChange={setLanguageModel}
                  />
                  <ChatSettings
                    languageModel={languageModel}
                    onLanguageModelChange={setLanguageModel}
                    apiKeyConfigurable={!process.env.NEXT_PUBLIC_NO_API_KEY_INPUT}
                    baseURLConfigurable={!process.env.NEXT_PUBLIC_NO_BASE_URL_INPUT}
                  />
                </AP3XChatInput>
              </div>
            </div>
          </aside>

          <div className="absolute top-0 bottom-0 right-0 left-[540px] p-3 pt-2">
            <div className="h-full flex flex-col bg-[#111111] rounded-xl">
              <PreviewControls result={currentPreview?.result} onRefresh={handleRefresh} />
              <main className="flex-1 relative bg-black overflow-hidden">
                <TabContent
                  fragment={currentPreview?.fragment}
                  result={currentPreview?.result}
                  refreshKey={refreshKey}
                />
              </main>
            </div>
          </div>
        </div>

        <AP3XAuthDialog
          open={authDialog}
          setOpen={setAuthDialog}
          supabase={supabase!}
          view={authView}
        />
      </div>
    </TabProvider>
  )
}


