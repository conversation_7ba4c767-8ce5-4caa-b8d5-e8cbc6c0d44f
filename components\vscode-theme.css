/* VS Code Dark Theme for Prism.js */
.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
  color: #6a9955;
  font-style: italic;
}

.token.punctuation {
  color: #d4d4d4;
}

.token.property,
.token.tag,
.token.boolean,
.token.number,
.token.constant,
.token.symbol,
.token.deleted {
  color: #b5cea8;
}

.token.selector,
.token.attr-name,
.token.string,
.token.char,
.token.builtin,
.token.inserted {
  color: #ce9178;
}

.token.operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string {
  color: #d4d4d4;
}

.token.atrule,
.token.attr-value,
.token.keyword {
  color: #569cd6;
}

.token.function,
.token.class-name {
  color: #dcdcaa;
}

.token.regex,
.token.important,
.token.variable {
  color: #d16969;
}

.token.important,
.token.bold {
  font-weight: bold;
}

.token.italic {
  font-style: italic;
}

.token.entity {
  cursor: help;
}

/* JSX/TSX specific tokens */
.token.tag .token.punctuation {
  color: #808080;
}

.token.tag .token.attr-name {
  color: #92c5f8;
}

.token.tag .token.attr-value {
  color: #ce9178;
}

.token.tag .token.script-punctuation,
.token.tag .token.attr-value .token.punctuation:first-child {
  color: #d4d4d4;
}

/* TypeScript specific */
.token.keyword.module,
.token.keyword.namespace,
.token.keyword.interface,
.token.keyword.type {
  color: #569cd6;
}

.token.builtin.class-name {
  color: #4ec9b0;
}

/* JSON specific */
.language-json .token.property {
  color: #92c5f8;
}

.language-json .token.string {
  color: #ce9178;
}

.language-json .token.number {
  color: #b5cea8;
}

.language-json .token.boolean {
  color: #569cd6;
}

.language-json .token.null {
  color: #569cd6;
}

/* CSS specific */
.language-css .token.selector {
  color: #d7ba7d;
}

.language-css .token.property {
  color: #92c5f8;
}

.language-css .token.function {
  color: #dcdcaa;
}

.language-css .token.important {
  color: #d16969;
}
